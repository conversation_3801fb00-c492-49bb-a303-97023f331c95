import { Telegraf } from "telegraf";
import dotenv from "dotenv";

dotenv.config();

/**
 * TelegramBot Class - Handles all Telegram bot interactions
 * Extracted from index.js to improve code organization and maintainability
 */
export class TelegramBot {
  constructor(options = {}) {
    // Bot Configuration
    this.token = options.token || process.env.TELEGRAM_BOT_TOKEN;
    this.chatId = options.chatId || process.env.TELEGRAM_GROUP_ID;
    this.symbol = options.symbol || process.env.SYMBOL || "ETHUSDT";
    
    if (!this.token) {
      throw new Error('Telegram bot token is required');
    }
    
    if (!this.chatId) {
      throw new Error('Telegram chat ID is required');
    }
    
    // Initialize Telegraf bot
    this.bot = new Telegraf(this.token);
    
    // Setup error handling
    this.bot.catch((err, ctx) => {
      console.error('Telegram bot error:', err);
    });
  }

  /**
   * Send a photo with caption to the configured chat
   * @param {string|Buffer} photo - Photo path or buffer
   * @param {string} caption - Message caption
   * @param {Object} options - Additional options
   * @returns {Promise} Telegram API response
   */
  async sendPhoto(photo, caption = '', options = {}) {
    try {
      const defaultOptions = {
        parse_mode: 'HTML',
        ...options
      };

      const photoSource = typeof photo === 'string' ? { source: photo } : photo;
      
      return await this.bot.telegram.sendPhoto(
        this.chatId,
        photoSource,
        {
          caption,
          ...defaultOptions
        }
      );
    } catch (error) {
      console.error('Error sending photo:', error);
      throw error;
    }
  }

  /**
   * Send a text message to the configured chat
   * @param {string} message - Message text
   * @param {Object} options - Additional options
   * @returns {Promise} Telegram API response
   */
  async sendMessage(message, options = {}) {
    try {
      const defaultOptions = {
        parse_mode: 'HTML',
        ...options
      };

      return await this.bot.telegram.sendMessage(
        this.chatId,
        message,
        defaultOptions
      );
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Send error notification to the chat
   * @param {Error} error - Error object
   * @param {string} context - Error context description
   * @returns {Promise} Telegram API response
   */
  async sendErrorNotification(error, context = 'Unknown') {
    const errorMessage = `⚠️ <b>Bot Error</b>\n\n` +
      `<b>Context:</b> ${context}\n` +
      `<b>Error:</b> ${error.message}\n` +
      `<b>Time:</b> ${new Date().toISOString()}`;

    return await this.sendMessage(errorMessage);
  }

  /**
   * Send critical error notification (for system failures)
   * @param {Error} error - Error object
   * @param {string} primaryError - Primary error message
   * @returns {Promise} Telegram API response
   */
  async sendCriticalErrorNotification(error, primaryError = '') {
    const errorMessage = `⚠️ <b>Critical Bot Error</b>\n\n` +
      `All analysis systems failed. Manual intervention required.\n\n` +
      `<b>Primary Error:</b> ${primaryError || error.message}`;

    return await this.sendMessage(errorMessage);
  }

  /**
   * Create enhanced caption for multi-timeframe analysis
   * @param {Object} multiTimeframeAnalysis - Analysis data
   * @param {Object} tradingSignal - Trading signal data
   * @param {string} analysis - GPT analysis text
   * @returns {string} Formatted caption
   */
  createEnhancedCaption(multiTimeframeAnalysis, tradingSignal, analysis) {
    const { majorTrend, trendAlignment, confidence } = tradingSignal;

    // Trend alignment indicators
    const alignmentEmoji = {
      'BULLISH_ALIGNED': '🟢🟢🟢',
      'BEARISH_ALIGNED': '🔴🔴🔴',
      'SIDEWAYS_ALIGNED': '⚪⚪⚪',
      'MIXED': '🟢🔴⚪'
    };

    const header = `📊 <b>${this.symbol} - Multi-Timeframe Analysis</b>\n`;
    const alignment = `${alignmentEmoji[trendAlignment.alignment] || '⚪'} <b>Alignment:</b> ${trendAlignment.alignment} (${(confidence * 100).toFixed(0)}%)\n`;
    const majorTrendInfo = `📈 <b>Major Trend:</b> ${majorTrend.trend} (${(majorTrend.strength * 100).toFixed(0)}%)\n\n`;

    return header + alignment + majorTrendInfo + analysis;
  }

  /**
   * Create AI-enhanced caption for AI vision analysis
   * @param {Object} aiTradingSignal - AI trading signal data
   * @param {string} analysis - AI analysis text
   * @returns {string} Formatted caption
   */
  createAIEnhancedCaption(aiTradingSignal, analysis) {
    const { major_trend, trading_recommendation, overall_assessment } = aiTradingSignal;

    // AI confidence indicators
    const confidenceEmoji = {
      10: '🟢🟢🟢', 9: '🟢🟢🟢', 8: '🟢🟢', 7: '🟢🟢',
      6: '🟢', 5: '🟡', 4: '🟡', 3: '🟠', 2: '🔴', 1: '🔴'
    };

    const confidence = overall_assessment.final_confidence;
    const confidenceIndicator = confidenceEmoji[confidence] || '⚪';

    const header = `🤖 <b>${this.symbol} - AI Vision Analysis</b>\n`;
    const aiConfidence = `${confidenceIndicator} <b>AI Confidence:</b> ${confidence}/10 (${overall_assessment.trade_quality})\n`;
    const recommendation = `🎯 <b>AI Signal:</b> ${trading_recommendation.action}\n\n`;

    return header + aiConfidence + recommendation + analysis;
  }

  /**
   * Create legacy caption for single-timeframe analysis
   * @param {string} interval - Trading interval
   * @param {string} analysis - Analysis text
   * @returns {string} Formatted caption
   */
  createLegacyCaption(interval, analysis) {
    return `📊 <b>${this.symbol} (${interval})</b>\n\n${analysis}`;
  }

  /**
   * Get bot instance for advanced usage
   * @returns {Telegraf} Telegraf bot instance
   */
  getBot() {
    return this.bot;
  }

  /**
   * Get current chat ID
   * @returns {string} Chat ID
   */
  getChatId() {
    return this.chatId;
  }

  /**
   * Set new chat ID
   * @param {string} chatId - New chat ID
   */
  setChatId(chatId) {
    this.chatId = chatId;
  }

  /**
   * Get current symbol
   * @returns {string} Current symbol
   */
  getSymbol() {
    return this.symbol;
  }

  /**
   * Set new symbol
   * @param {string} symbol - New symbol
   */
  setSymbol(symbol) {
    this.symbol = symbol;
  }

  /**
   * Start the bot (for webhook or polling mode)
   * @param {Object} options - Start options
   */
  start(options = {}) {
    if (options.webhook) {
      this.bot.launch({
        webhook: options.webhook
      });
    } else {
      this.bot.launch();
    }
    
    console.log('Telegram bot started');
    
    // Enable graceful stop
    process.once('SIGINT', () => this.bot.stop('SIGINT'));
    process.once('SIGTERM', () => this.bot.stop('SIGTERM'));
  }

  /**
   * Stop the bot
   */
  stop() {
    this.bot.stop();
    console.log('Telegram bot stopped');
  }
}

// Create default instance for backward compatibility
export const telegramBot = new TelegramBot();

// Export bot instance for backward compatibility
export const bot = telegramBot.getBot();
export const chatId = telegramBot.getChatId();
