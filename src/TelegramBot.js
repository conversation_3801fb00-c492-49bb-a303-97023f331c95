import { Telegraf } from "telegraf";
import dotenv from "dotenv";

dotenv.config();

/**
 * TelegramBot Class - Handles all Telegram bot interactions
 * Extracted from index.js to improve code organization and maintainability
 */
export class TelegramBot {
  constructor(options = {}) {
    // Bot Configuration
    this.token = options.token || process.env.TELEGRAM_BOT_TOKEN;
    this.chatId = options.chatId || process.env.TELEGRAM_GROUP_ID;
    this.symbol = options.symbol || process.env.SYMBOL || "ETHUSDT";
    
    if (!this.token) {
      throw new Error('Telegram bot token is required');
    }
    
    if (!this.chatId) {
      throw new Error('Telegram chat ID is required');
    }
    
    // Initialize Telegraf bot
    this.bot = new Telegraf(this.token);
    
    // Setup error handling
    this.bot.catch((err, ctx) => {
      console.error('Telegram bot error:', err);
    });
  }

  /**
   * Send a photo with caption to the configured chat
   * @param {string|Buffer} photo - Photo path or buffer
   * @param {string} caption - Message caption
   * @param {Object} options - Additional options
   * @returns {Promise} Telegram API response
   */
  async sendPhoto(photo, caption = '', options = {}) {
    try {
      const defaultOptions = {
        parse_mode: 'HTML',
        ...options
      };

      const photoSource = typeof photo === 'string' ? { source: photo } : photo;
      
      return await this.bot.telegram.sendPhoto(
        this.chatId,
        photoSource,
        {
          caption,
          ...defaultOptions
        }
      );
    } catch (error) {
      console.error('Error sending photo:', error);
      throw error;
    }
  }

  /**
   * Send a text message to the configured chat
   * @param {string} message - Message text
   * @param {Object} options - Additional options
   * @returns {Promise} Telegram API response
   */
  async sendMessage(message, options = {}) {
    try {
      const defaultOptions = {
        parse_mode: 'HTML',
        ...options
      };

      return await this.bot.telegram.sendMessage(
        this.chatId,
        message,
        defaultOptions
      );
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Send error notification to the chat
   * @param {Error} error - Error object
   * @param {string} context - Error context description
   * @returns {Promise} Telegram API response
   */
  async sendErrorNotification(error, context = 'Unknown') {
    const errorMessage = `⚠️ <b>Bot Error</b>\n\n` +
      `<b>Context:</b> ${context}\n` +
      `<b>Error:</b> ${error.message}\n` +
      `<b>Time:</b> ${new Date().toISOString()}`;

    return await this.sendMessage(errorMessage);
  }

  /**
   * Send critical error notification (for system failures)
   * @param {Error} error - Error object
   * @param {string} primaryError - Primary error message
   * @returns {Promise} Telegram API response
   */
  async sendCriticalErrorNotification(error, primaryError = '') {
    const errorMessage = `⚠️ <b>Critical Bot Error</b>\n\n` +
      `All analysis systems failed. Manual intervention required.\n\n` +
      `<b>Primary Error:</b> ${primaryError || error.message}`;

    return await this.sendMessage(errorMessage);
  }

  /**
   * Create enhanced caption for multi-timeframe analysis
   * @param {Object} multiTimeframeAnalysis - Analysis data
   * @param {Object} tradingSignal - Trading signal data
   * @param {string} analysis - GPT analysis text
   * @returns {string} Formatted caption
   */
  createEnhancedCaption(multiTimeframeAnalysis, tradingSignal, analysis) {
    const { majorTrend, trendAlignment, confidence } = tradingSignal;

    // Trend alignment indicators
    const alignmentEmoji = {
      'BULLISH_ALIGNED': '🟢🟢🟢',
      'BEARISH_ALIGNED': '🔴🔴🔴',
      'SIDEWAYS_ALIGNED': '⚪⚪⚪',
      'MIXED': '🟢🔴⚪'
    };

    const header = `📊 <b>${this.symbol} - Multi-Timeframe Analysis</b>\n`;
    const alignment = `${alignmentEmoji[trendAlignment.alignment] || '⚪'} <b>Alignment:</b> ${trendAlignment.alignment} (${(confidence * 100).toFixed(0)}%)\n`;
    const majorTrendInfo = `📈 <b>Major Trend:</b> ${majorTrend.trend} (${(majorTrend.strength * 100).toFixed(0)}%)\n\n`;

    return header + alignment + majorTrendInfo + analysis;
  }

  /**
   * Create AI-enhanced caption for AI vision analysis
   * @param {Object} aiTradingSignal - AI trading signal data
   * @param {string} analysis - AI analysis text
   * @returns {string} Formatted caption
   */
// Tạo caption Telegram phong cách pro trader với kịch bản breakout rõ ràng
  createAIEnhancedCaption(aiTradingSignal, analysis) {
    const {
      major_trend = {},
      trading_recommendation: tr = {},
      risk_assessment: risk = {},
      timeframe_alignment: tf = {},
      overall_assessment: overall = {},
      execution_plan: execPlan = {},
      metadata = {}
    } = aiTradingSignal || {};

    const symbol =
      metadata.symbol || this?.symbol || metadata?.SYMBOL || "SYMBOL";
    const timeframes = Array.isArray(metadata.timeframes_analyzed)
      ? metadata.timeframes_analyzed.join("/")
      : "4H/1H/15M";

    // Emojis
    const confEmoji = {
      10: "🟢🟢🟢", 9: "🟢🟢🟢", 8: "🟢🟢", 7: "🟢🟢",
      6: "🟢", 5: "🟡", 4: "🟡", 3: "🟠", 2: "🔴", 1: "🔴"
    };
    const signalEmoji = {
      STRONG_BUY: "🚀", BUY: "📈", HOLD: "⏸️",
      SELL: "📉", STRONG_SELL: "💥", NO_TRADE: "⏹️"
    };
    const alignEmoji = {
      FULLY_ALIGNED: "✅", PARTIALLY_ALIGNED: "⚠️", CONFLICTING: "❌"
    };

    const confidence = Number(overall.final_confidence) || 0;
    const confIcon = confEmoji[confidence] || "⚪";
    const action = tr.action || "NO_TRADE";
    const actionIcon = signalEmoji[action] || "⏹️";
    const rr = tr.risk_reward_ratio || "N/A";
    const tp1 = tr.take_profit_1 || "N/A";
    const tp2 = tr.take_profit_2 || null;
    const entry = tr.entry_price || "N/A";
    const sl = tr.stop_loss || (risk.invalidation_level || "N/A");
    const tradeQuality = overall.trade_quality || "N/A";
    const trendDir = major_trend.direction || "N/A";
    const trendStr = major_trend.strength != null ? major_trend.strength : "N/A";
    const alignType = tf.alignment_type || "PARTIALLY_ALIGNED";
    const alignIcon = alignEmoji[alignType] || "⚠️";

    // Làm sạch analysis: bỏ ``` và thừa khoảng trắng
    const cleanAnalysis = (analysis || "")
      .replace(/```(?:html|json)?/gi, "")
      .replace(/```/g, "")
      .replace(/\s+\n/g, "\n")
      .trim();

    // Sinh trigger breakout có điều kiện (chuẩn hoá cho cả BUY/SELL)
    const isBuy = action === "BUY" || action === "STRONG_BUY";
    const isSell = action === "SELL" || action === "STRONG_SELL";

    const dirWord = isBuy ? "trên" : "dưới";
    const antiDirWord = isBuy ? "dưới" : "trên";

    // Trigger A: xác nhận breakout
    const triggerA = (entry === "N/A")
      ? `Kích hoạt: nến 15M/1H đóng ${isBuy ? "tăng" : "giảm"} mạnh; Volume > MA20; thân nến đóng ngoài PAC; RSI ${isBuy ? ">" : "<"} 50, không phân kỳ.`
      : `Kích hoạt: nến 15M (ưu tiên 1H) đóng ${dirWord} ${entry}; Volume > MA20; thân nến đóng ngoài PAC/Dragon; RSI ${isBuy ? ">" : "<"} 50, không phân kỳ.`;

    // Entry A: entry theo phá vỡ/hoặc retest
    const entryA = (entry === "N/A")
      ? `${isBuy ? "Buy" : "Sell"} theo break hoặc chờ retest PAC/đỉnh đáy vừa phá.`
      : `${isBuy ? "Buy" : "Sell"} khi đóng ${dirWord} ${entry} hoặc limit tại vùng retest quanh ${entry}.`;

    // Trigger B: fail breakout / fakeout đảo chiều
    const triggerB = (entry === "N/A")
      ? `Fail: giá phá nhưng hút lại vào trong vùng tích luỹ; Volume suy yếu; RSI xuất hiện phân kỳ đảo chiều.`
      : `Fail: sau khi phá ${dirWord} ${entry}, giá đóng lại ${antiDirWord} ${entry} với Volume giảm/từ chối mạnh (wick dài).`;

    // Entry B: nếu fail thì ưu tiên NO_TRADE hoặc phản hướng
    const entryB = isBuy
      ? `Nếu fakeout, tránh long đuổi; cân nhắc short scalp chỉ khi 1H xác nhận đóng ${antiDirWord} ${entry} + Volume tăng.`
      : `Nếu fakeout, tránh short đuổi; cân nhắc long scalp chỉ khi 1H xác nhận đóng ${antiDirWord} ${entry} + Volume tăng.`;

    // Take profit & quản lý lệnh
    const tpText = tp2 ? `${tp1} / ${tp2}` : `${tp1}`;
    const manageText =
      execPlan.exit_strategy
        ? execPlan.exit_strategy
        : `Chốt một phần ở TP1 (${tp1}), dời SL về BE khi đạt R:R ≥ 1:1; trailing theo PAC/EMA89.`;

    // Monitoring
    const watchLevels =
      Array.isArray(execPlan.monitoring_points) && execPlan.monitoring_points.length
        ? execPlan.monitoring_points.join(", ")
        : "Breakout zone, PAC (EMA Hi/Lo), EMA89/200, vùng thanh khoản gần nhất.";

    // Header
    const header =
      `🤖 <b>${symbol} — Pro Trader Plan (${timeframes})</b>\n` +
      `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;

    // Khối tóm tắt
    const summary =
      `🌊 <b>Trend:</b> ${trendDir} (${trendStr}/10)  ${alignIcon} <i>${alignType.replaceAll("_"," ")}</i>\n` +
      `${actionIcon} <b>Signal:</b> ${action} | 🎯 <b>Entry:</b> ${entry}\n` +
      `🛡️ <b>SL:</b> ${sl} | 🏁 <b>TP:</b> ${tpText} | 📊 <b>R/R:</b> ${rr}\n` +
      `${confIcon} <b>Confidence:</b> ${confidence}/10 <i>(${tradeQuality})</i>\n`;

    // Kịch bản giao dịch
    const scenarios =
      `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n` +
      `📈 <b>Scenario A — Breakout Confirmed</b>\n` +
      `• Trigger: ${triggerA}\n` +
      `• Entry: ${entryA}\n` +
      `• SL: ${sl} | TP: ${tpText}\n` +
      `• Quản lý: ${manageText}\n` +
      `\n` +
      `📉 <b>Scenario B — Fakeout/Fail</b>\n` +
      `• Trigger: ${triggerB}\n` +
      `• Hành động: ${entryB}\n` +
      `• Invalidation: phá ngược và giữ giá qua 1–2 nến 15M/1H.\n`;

    // Monitoring
    const monitoring =
      `🛰️ <b>Monitoring:</b> ${watchLevels}\n`;

    // Insight ngắn (nếu muốn kèm)
    const insights = cleanAnalysis
      ? `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n` +
        `📌 <b>Notes:</b> ${cleanAnalysis}`
      : "";

    // (Tùy chọn) Lưu ý rủi ro ngắn gọn
    const footer =
      `\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n` +
      `⚠️ <i>Kế hoạch mang tính tham khảo, tự quản trị rủi ro.</i>`;

    return header + summary + scenarios + monitoring + insights + footer;
  }



  /**
   * Create legacy caption for single-timeframe analysis
   * @param {string} interval - Trading interval
   * @param {string} analysis - Analysis text
   * @returns {string} Formatted caption
   */
  createLegacyCaption(interval, analysis) {
    return `📊 <b>${this.symbol} (${interval})</b>\n\n${analysis}`;
  }

  /**
   * Get bot instance for advanced usage
   * @returns {Telegraf} Telegraf bot instance
   */
  getBot() {
    return this.bot;
  }

  /**
   * Get current chat ID
   * @returns {string} Chat ID
   */
  getChatId() {
    return this.chatId;
  }

  /**
   * Set new chat ID
   * @param {string} chatId - New chat ID
   */
  setChatId(chatId) {
    this.chatId = chatId;
  }

  /**
   * Get current symbol
   * @returns {string} Current symbol
   */
  getSymbol() {
    return this.symbol;
  }

  /**
   * Set new symbol
   * @param {string} symbol - New symbol
   */
  setSymbol(symbol) {
    this.symbol = symbol;
  }

  /**
   * Start the bot (for webhook or polling mode)
   * @param {Object} options - Start options
   */
  start(options = {}) {
    if (options.webhook) {
      this.bot.launch({
        webhook: options.webhook
      });
    } else {
      this.bot.launch();
    }
    
    console.log('Telegram bot started');
    
    // Enable graceful stop
    process.once('SIGINT', () => this.bot.stop('SIGINT'));
    process.once('SIGTERM', () => this.bot.stop('SIGTERM'));
  }

  /**
   * Stop the bot
   */
  stop() {
    this.bot.stop();
    console.log('Telegram bot stopped');
  }
}

// Create default instance for backward compatibility
export const telegramBot = new TelegramBot();

// Export bot instance for backward compatibility
export const bot = telegramBot.getBot();
export const chatId = telegramBot.getChatId();
